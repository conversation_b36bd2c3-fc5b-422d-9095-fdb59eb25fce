// app.js
const errorHandler = require('./utils/errorHandler.js');
const cloudStorage = require('./utils/cloudStorage.js');
const userActivityTracker = require('./utils/userActivityTracker.js');
// const UnreadMessageDebugger = require('./utils/unreadMessageDebugger.js'); // 暂时注释，避免启动错误

App({
  onLaunch() {
    // 初始化错误处理
    this.globalData.errorHandler = errorHandler;

    // 初始化全局已处理通知记录
    this.globalProcessedNotifications = new Set();

    // 异步初始化云开发环境，避免阻塞启动
    this.initCloudAsync();

    // 快速检查登录状态
    this.checkLoginStatus();

    // 初始化通知系统
    this.initNotificationSystem();

    // 初始化未读消息状态
    try {
      this.globalData.unreadMessages.init();
    } catch (error) {
      console.error('📱 [App] 初始化未读消息状态失败:', error);
    }

    // 初始化撤回消息状态
    try {
      this.globalData.recalledMessages.init();
    } catch (error) {
      console.error('📱 [App] 初始化撤回消息状态失败:', error);
    }

    // 启动全局聊天消息监听器
    this.startGlobalChatWatcher();

    // 延迟触发未读消息更新事件，确保TabBar正确显示
    setTimeout(() => {
      if (this.globalData.unreadMessages) {
        this.$emit('unreadMessageUpdate', {
          chatRoomId: 'init',
          unreadData: this.globalData.unreadMessages
        });
        console.log('📱 [App] 启动时触发未读消息更新事件，总未读数:', this.globalData.unreadMessages.total);
      }
    }, 2000);

    // 初始化简化调试工具
    if (typeof console !== 'undefined') {
      const app = this;
      console.unreadDebug = {
        status: () => {
          const status = {
            total: app.globalData.unreadMessages.total,
            chatRooms: app.globalData.unreadMessages.chatRooms,
            lastHideTime: app.globalData.lastHideTime,
            lastActiveTime: app.globalData.lastActiveTime
          };
          console.log('📊 [未读调试] 当前状态:', status);
          return status;
        },
        refresh: () => {
          app.$emit('unreadMessageUpdate', {
            chatRoomId: 'debug_refresh',
            unreadData: app.globalData.unreadMessages
          });
          console.log('🔄 [未读调试] 强制刷新完成');
        },
        check: () => {
          if (app.checkOfflineMessages) {
            app.checkOfflineMessages();
            console.log('🧪 [未读调试] 离线消息检查已触发');
          }
        }
      };
      console.log('🛠️ [App] 简化调试工具已初始化，使用 console.unreadDebug 访问');
    }
  },

  // 异步初始化云开发
  async initCloudAsync() {
    try {
      if (!wx.cloud) {
        console.error('❌ [云开发] wx.cloud 不存在，基础库版本过低');
        errorHandler.handle(new Error('云开发基础库版本过低'), '云开发初始化');
        return;
      }

      await wx.cloud.init({
        env: 'cloud1-9gsj7t48183e5a9f', // 云开发环境ID
        traceUser: false, // 关闭用户追踪减少性能开销
      });

      // 设置云存储管理器的环境ID
      cloudStorage.setEnvId('cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750');

      this.globalData.cloudReady = true;

    } catch (error) {
      console.error('❌ [云开发] 初始化失败:', error);
      errorHandler.handle(error, '云开发初始化');
      this.globalData.cloudReady = false;
    }
  },

  onShow() {
    // 小程序显示时的处理
    this.globalData.appInBackground = false;

    // 记录用户活跃
    if (this.globalData.userInfo) {
      userActivityTracker.recordActivity();
    }

    // 重新启动全局通知监听（如果需要）
    if (this.globalData.userInfo && !this.globalNotificationWatcher) {
      this.setupGlobalNotificationWatcher();
    }

    // 重新启动全局聊天监听（如果需要）
    if (this.globalData.userInfo && !this.globalChatWatcher) {
      this.setupGlobalChatWatcher();
    }

    // 🔄 检查离线期间的新消息（延迟执行，确保监听器已启动）
    if (this.globalData.userInfo) {
      setTimeout(() => {
        this.checkOfflineMessages();
      }, 1000); // 延迟1秒，确保监听器完全启动
    }
  },

  onHide() {
    // 小程序隐藏时的处理
    this.globalData.appInBackground = true;

    // 记录小程序隐藏时间，用于后续检查离线消息
    this.globalData.lastHideTime = new Date();

    // 强制更新用户活跃时间
    if (this.globalData.userInfo) {
      userActivityTracker.forceUpdate();
    }

    // 暂停全局通知监听以节省资源
    if (this.globalNotificationWatcher) {
      this.globalNotificationWatcher.close();
      this.globalNotificationWatcher = null;
    }

    // 暂停全局聊天监听以节省资源
    if (this.globalChatWatcher) {
      this.globalChatWatcher.close();
      this.globalChatWatcher = null;
    }

    // 清理已处理通知记录，避免内存泄漏
    if (this.globalProcessedNotifications) {
      this.globalProcessedNotifications.clear();
    }

    // 清理已处理聊天消息记录，避免内存泄漏
    if (this.processedChatMessages) {
      this.processedChatMessages.clear();
    }
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.openid) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLogin = true;
    } else {
      this.globalData.isLogin = false;
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLogin: false,
    cloudEnv: 'cloud1-9gsj7t48183e5a9f',
    cloudReady: false,
    errorHandler: null,
    eventBus: new Map(), // 事件总线
    enterGrabMode: false, // 标记是否需要进入抢单大厅模式
    appInBackground: false, // 应用是否在后台
    lastHideTime: null, // 记录小程序最后隐藏时间，用于检查离线消息
    lastActiveTime: null, // 记录用户最后活跃时间（阅读消息时更新）

    // 撤回消息状态管理
    recalledMessages: {
      // 存储撤回的消息状态 { chatRoomId: { content: '[消息已撤回]', timestamp: Date.now() } }
      rooms: {},

      // 标记消息为已撤回
      markAsRecalled: function(chatRoomId, content = '[消息已撤回]', messageTime = null) {
        const recallTimestamp = Date.now();

        this.rooms[chatRoomId] = {
          content: content,
          timestamp: recallTimestamp,
          messageTime: messageTime || recallTimestamp, // 被撤回消息的原始时间
          isRecalled: true
        };

        // 保存到本地存储
        try {
          wx.setStorageSync('recalledMessages', this.rooms);
        } catch (error) {
          console.error('❌ [全局撤回] 保存撤回状态失败:', error);
        }

        // 触发全局事件，通知所有页面更新
        const app = getApp();
        app.$emit('messageRecalled', {
          chatRoomId: chatRoomId,
          content: content,
          timestamp: recallTimestamp,
          messageTime: messageTime
        });

        // 额外的强制通知机制
        setTimeout(() => {
          app.$emit('forceRefreshChatList', {
            reason: 'messageRecalled',
            chatRoomId: chatRoomId
          });
        }, 100);
      },

      // 检查聊天室是否有撤回消息
      isRecalled: function(chatRoomId) {
        const recalled = this.rooms[chatRoomId];
        if (!recalled) {
          return null;
        }

        // 检查是否在有效期内（1小时）
        const age = Date.now() - recalled.timestamp;
        const isValid = age < 3600000;

        if (!isValid) {
          // 清除过期的撤回状态
          delete this.rooms[chatRoomId];
          try {
            wx.setStorageSync('recalledMessages', this.rooms);
          } catch (error) {
            console.error('❌ [全局撤回] 清除过期状态失败:', error);
          }
          return null;
        }

        return recalled;
      },

      // 检查撤回状态是否比指定消息更新
      isRecallNewerThan: function(chatRoomId, messageTime) {
        const recalled = this.isRecalled(chatRoomId);
        if (!recalled) {
          return false;
        }

        const recallTime = recalled.timestamp;
        const msgTime = new Date(messageTime).getTime();

        return recallTime > msgTime;
      },

      // 初始化撤回状态（从本地存储恢复）
      init: function() {
        try {
          const stored = wx.getStorageSync('recalledMessages');
          if (stored && typeof stored === 'object') {
            this.rooms = stored;
          }
        } catch (error) {
          console.error('❌ [全局撤回] 恢复撤回状态失败:', error);
        }
      },

      // 清除指定聊天室的撤回状态
      clear: function(chatRoomId) {
        const hadRecallStatus = !!this.rooms[chatRoomId];

        if (hadRecallStatus) {
          // 从内存中删除
          delete this.rooms[chatRoomId];

          // 更新本地存储
          try {
            wx.setStorageSync('recalledMessages', this.rooms);
          } catch (error) {
            console.error('❌ [全局撤回] 清除撤回状态失败:', error);
          }

          // 触发全局事件，通知聊天列表清除撤回状态
          const app = getApp();
          app.$emit('recallStatusCleared', {
            chatRoomId: chatRoomId,
            timestamp: Date.now()
          });
        }
      }
    },

    // 未读消息状态管理
    unreadMessages: {
      total: 0,                    // 总未读数
      chatRooms: {},               // 各聊天室未读状态
      lastUpdateTime: null,        // 最后更新时间

      // 未读消息管理方法
      updateUnread: function(chatRoomId, increment = 1) {
        const app = getApp();

        console.log('📱 [未读管理] 更新未读数:', {
          chatRoomId,
          increment,
          activeChatRoomId: app.globalData.activeChatRoomId,
          isActive: chatRoomId === app.globalData.activeChatRoomId
        });

        // 如果是当前活跃的聊天室，不增加未读数
        if (chatRoomId === app.globalData.activeChatRoomId) {
          console.log('📱 [未读管理] 跳过活跃聊天室的未读数更新');
          return;
        }

        // 更新特定聊天室的未读数
        if (!this.chatRooms[chatRoomId]) {
          this.chatRooms[chatRoomId] = {
            count: 0,
            lastMessage: null,
            lastUpdateTime: null
          };
        }

        const oldCount = this.chatRooms[chatRoomId].count;
        this.chatRooms[chatRoomId].count += increment;
        this.chatRooms[chatRoomId].lastUpdateTime = new Date();

        console.log('📱 [未读管理] 未读数已更新:', {
          chatRoomId,
          oldCount,
          newCount: this.chatRooms[chatRoomId].count,
          increment
        });

        // 重新计算总未读数
        this.total = Object.values(this.chatRooms)
          .reduce((total, room) => total + room.count, 0);

        this.lastUpdateTime = new Date();

        // 保存到本地缓存
        try {
          wx.setStorageSync('unreadMessages', this);
        } catch (error) {
          console.error('📱 [UnreadMessages] 保存状态失败:', error);
        }

        // 触发事件
        app.$emit('unreadMessageUpdate', {
          chatRoomId,
          unreadData: { ...this }
        });
      },

      clearUnread: function(chatRoomId) {
        const app = getApp();

        if (this.chatRooms[chatRoomId]) {
          this.chatRooms[chatRoomId].count = 0;
          this.chatRooms[chatRoomId].lastUpdateTime = new Date();
        }

        // 重新计算总未读数
        this.total = Object.values(this.chatRooms)
          .reduce((total, room) => total + room.count, 0);

        this.lastUpdateTime = new Date();

        // 保存到本地缓存
        try {
          wx.setStorageSync('unreadMessages', this);
        } catch (error) {
          console.error('📱 [UnreadMessages] 保存状态失败:', error);
        }

        // 触发事件
        app.$emit('unreadMessageUpdate', {
          chatRoomId,
          unreadData: { ...this }
        });
      },

      getChatRoomCount: function(chatRoomId) {
        const count = this.chatRooms[chatRoomId]?.count || 0;
        console.log('📱 [未读管理] 获取聊天室未读数:', chatRoomId, '未读数:', count);
        return count;
      },

      updateLastMessage: function(chatRoomId, messageData) {
        if (!this.chatRooms[chatRoomId]) {
          this.chatRooms[chatRoomId] = {
            count: 0,
            lastMessage: null,
            lastUpdateTime: null
          };
        }

        this.chatRooms[chatRoomId].lastMessage = {
          content: messageData.content,
          type: messageData.type,
          senderId: messageData.senderId,
          createTime: messageData.createTime
        };
        this.chatRooms[chatRoomId].lastUpdateTime = new Date();

        // 保存到本地缓存
        try {
          wx.setStorageSync('unreadMessages', this);
        } catch (error) {
          console.error('📱 [UnreadMessages] 保存状态失败:', error);
        }
      },

      init: function() {
        try {
          const cachedState = wx.getStorageSync('unreadMessages');
          console.log('📱 [UnreadMessages] 从本地存储恢复状态:', cachedState);

          if (cachedState && cachedState.chatRooms) {
            this.total = cachedState.total || 0;
            this.chatRooms = cachedState.chatRooms || {};
            this.lastUpdateTime = cachedState.lastUpdateTime;

            console.log('📱 [UnreadMessages] 状态恢复成功:', {
              total: this.total,
              chatRoomsCount: Object.keys(this.chatRooms).length,
              lastUpdateTime: this.lastUpdateTime
            });
          } else {
            console.log('📱 [UnreadMessages] 没有缓存状态，使用默认值');
          }
        } catch (error) {
          console.error('📱 [UnreadMessages] 恢复状态失败:', error);
        }
      }
    },
    // 当前活跃的聊天室ID（用于判断是否需要提醒）
    activeChatRoomId: null,

    // 设置活跃聊天室的方法
    setActiveChatRoom: function(chatRoomId) {
      const previousChatRoomId = this.activeChatRoomId;
      this.activeChatRoomId = chatRoomId;

      // 如果进入了新的聊天室，清除该聊天室的未读数
      if (chatRoomId && chatRoomId !== previousChatRoomId) {
        this.unreadMessages.clearUnread(chatRoomId);

        // 更新最后检查时间，避免重复显示已读消息
        const app = getApp();
        if (app.updateLastCheckTime) {
          app.updateLastCheckTime();
        }
      }

      // 触发事件
      const app = getApp();
      app.$emit('activeChatRoomChange', {
        previousChatRoomId,
        currentChatRoomId: chatRoomId
      });
    }
  },

  // 事件总线方法
  $on(event, callback) {
    if (!this.globalData.eventBus.has(event)) {
      this.globalData.eventBus.set(event, []);
    }
    this.globalData.eventBus.get(event).push(callback);
  },

  $emit(event, data) {
    if (this.globalData.eventBus.has(event)) {
      this.globalData.eventBus.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Event callback error:', error);
        }
      });
    }
  },

  $off(event, callback) {
    if (this.globalData.eventBus.has(event)) {
      const callbacks = this.globalData.eventBus.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  },

  // 更新用户信息并触发事件
  updateUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
    wx.setStorageSync('userInfo', userInfo);
    this.$emit('userInfoUpdated', userInfo);

    // 启动用户活跃时间跟踪
    if (userInfo && userInfo._id) {
      userActivityTracker.init();
    }
  },



  // 初始化通知系统
  initNotificationSystem() {
    // 暂时禁用通知系统，避免重复日志
    // this.setupNotificationListeners();
  },

  // 请求订阅消息权限
  async requestSubscribeMessage() {
    try {
      const templateIds = [
        'your_order_status_template_id',
        'your_new_order_template_id',
        'your_chat_message_template_id'
      ];

      await wx.requestSubscribeMessage({
        tmplIds: templateIds
      });
    } catch (error) {
      console.log('订阅消息权限请求失败:', error);
    }
  },

  // 设置通知监听器
  setupNotificationListeners() {
    // 监听订单状态变更
    this.$on('orderStatusChanged', (data) => {
      console.log('订单状态变更通知:', data);
    });

    // 监听新订单
    this.$on('newOrderReceived', (data) => {
      console.log('新订单通知:', data);
    });

    // 监听聊天消息
    this.$on('newChatMessage', (data) => {
      console.log('新聊天消息通知:', data);
    });

    // 启动全局通知监听
    this.startGlobalNotificationWatcher();
  },

  // 启动全局通知监听
  startGlobalNotificationWatcher() {
    // 延迟启动，确保云开发已初始化且用户已登录
    setTimeout(() => {
      this.setupGlobalNotificationWatcher();
    }, 3000);
  },

  // 设置全局通知监听器
  setupGlobalNotificationWatcher() {
    if (!this.globalData.cloudReady || !this.globalData.userInfo || !this.globalData.userInfo._id) {
      console.log('📢 [全局通知] 条件不满足，延迟启动');
      console.log('📢 [全局通知] cloudReady:', this.globalData.cloudReady);
      console.log('📢 [全局通知] userInfo:', this.globalData.userInfo);
      setTimeout(() => {
        this.setupGlobalNotificationWatcher();
      }, 2000);
      return;
    }

    console.log('📢 [全局通知] 启动全局通知监听器，用户ID:', this.globalData.userInfo._id);

    try {
      // 监听当前用户的所有未读通知
      this.globalNotificationWatcher = wx.cloud.database().collection('notifications')
        .where({
          receiverId: this.globalData.userInfo._id,
          status: 'unread'
        })
        .watch({
          onChange: (snapshot) => {
            console.log('📢 [全局通知] 收到通知变化:', snapshot);
            console.log('📢 [全局通知] docChanges:', snapshot.docChanges);

            if (snapshot.docChanges) {
              snapshot.docChanges.forEach(change => {
                console.log('📢 [全局通知] 处理变更:', change.changeType, change.doc);

                // 只处理真正新增的通知，跳过初始化时的历史通知
                if (change.changeType === 'add') {
                  console.log('📢 [全局通知] 检测到新通知，准备处理...');
                  this.handleGlobalNotification(change.doc);
                } else if (change.changeType === undefined && snapshot.type !== 'init') {
                  // 只有在非初始化时才处理 changeType 为 undefined 的情况
                  console.log('📢 [全局通知] 检测到新通知（changeType undefined），准备处理...');
                  this.handleGlobalNotification(change.doc);
                } else {
                  console.log('📢 [全局通知] 跳过处理:', {
                    changeType: change.changeType,
                    snapshotType: snapshot.type,
                    reason: change.changeType === undefined && snapshot.type === 'init' ? '初始化历史通知' : '非新增通知'
                  });
                }
              });
            }
          },
          onError: (error) => {
            console.error('❌ [全局通知] 监听失败:', error);
            // 重新启动监听器
            setTimeout(() => {
              this.setupGlobalNotificationWatcher();
            }, 5000);
          }
        });



      // 清理历史通知（可选）
      this.cleanupOldNotifications();
    } catch (error) {
      console.error('❌ [全局通知] 启动失败:', error);
    }
  },

  // 清理旧的通知
  async cleanupOldNotifications() {
    try {
      // 清理超过24小时的已读通知
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const result = await wx.cloud.database().collection('notifications')
        .where({
          receiverId: this.globalData.userInfo._id,
          status: 'read',
          createTime: wx.cloud.database().command.lt(oneDayAgo)
        })
        .remove();

    } catch (error) {
      // 静默处理清理失败
    }
  },

  // 处理全局通知
  handleGlobalNotification(notification) {
    console.log('📢 [全局通知] 处理通知:', notification);

    // 检查是否已经处理过这个通知
    const notificationId = notification._id;
    if (this.globalProcessedNotifications.has(notificationId)) {
      console.log('📢 [全局通知] 通知已处理，跳过:', notificationId);
      return;
    }

    // 标记为已处理
    this.globalProcessedNotifications.add(notificationId);

    // 额外检查：确保不处理发给自己的通知（如果是自己发送的）
    const currentUserId = this.globalData.userInfo?._id;
    if (notification.senderId && String(notification.senderId) === String(currentUserId)) {
      console.log('⚠️ [全局通知] 跳过自己发送的通知:', {
        senderId: notification.senderId,
        currentUserId: currentUserId
      });

      // 仍然标记为已读，但不显示
      wx.cloud.database().collection('notifications')
        .doc(notification._id)
        .update({
          data: { status: 'read' }
        });
      return;
    }

    switch (notification.type) {
      case 'order_cancelled':
        this.showOrderCancelledNotification(notification);
        break;
      case 'order_accepted':
        this.showOrderAcceptedNotification(notification);
        break;
      case 'order_completed':
        this.showOrderCompletedNotification(notification);
        break;
      default:
        console.log('📢 [全局通知] 未知通知类型:', notification.type);
        break;
    }

    // 标记通知为已读
    wx.cloud.database().collection('notifications')
      .doc(notification._id)
      .update({
        data: { status: 'read' }
      });
  },

  // 显示订单取消通知
  showOrderCancelledNotification(notification) {
    console.log('📢 [全局通知] 显示取消通知详情:', {
      title: notification.title,
      content: notification.content,
      receiverId: notification.receiverId,
      senderId: notification.senderId,
      currentUserId: this.globalData.userInfo?._id
    });

    // 检查当前页面是否是订单详情页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const isOrderDetailPage = currentPage && currentPage.route === 'pages/order/detail/detail';

    if (isOrderDetailPage) {
      // 如果当前在订单详情页面，让页面自己处理通知
      console.log('📢 [全局通知] 当前在订单详情页面，跳过全局通知显示');
      return;
    }

    // 格式化通知内容
    const title = notification.title || '订单已取消';
    const content = notification.content || '此订单已被取消';

    console.log('📢 [全局通知] 最终显示内容:', { title, content });

    wx.showModal({
      title: title,
      content: content,
      showCancel: true,
      confirmText: '查看详情',
      cancelText: '我知道了',
      confirmColor: '#00d4ff',
      cancelColor: '#999999',
      success: (res) => {
        if (res.confirm && notification.orderId) {
          // 跳转到订单详情页面
          wx.navigateTo({
            url: `/order-package/pages/detail/detail?id=${notification.orderId}`
          });
        }
        // 触发事件，让相关页面刷新
        this.$emit('orderCancelled', {
          orderId: notification.orderId,
          orderNo: notification.orderNo
        });
      }
    });
  },

  // 显示订单接单通知
  showOrderAcceptedNotification(notification) {
    wx.showModal({
      title: notification.title || '订单已接单',
      content: notification.content || '您的订单已被接单',
      showCancel: false,
      confirmText: '查看详情',
      confirmColor: '#00d4ff',
      success: (res) => {
        if (res.confirm && notification.orderId) {
          wx.navigateTo({
            url: `/order-package/pages/detail/detail?id=${notification.orderId}`
          });
        }
      }
    });
  },

  // 显示订单完成通知
  showOrderCompletedNotification(notification) {
    wx.showModal({
      title: notification.title || '订单已完成',
      content: notification.content || '订单已完成，请评价',
      showCancel: false,
      confirmText: '去评价',
      confirmColor: '#00d4ff',
      success: (res) => {
        if (res.confirm && notification.orderId) {
          wx.navigateTo({
            url: `/order-package/pages/evaluation/evaluation?orderId=${notification.orderId}`
          });
        }
      }
    });
  },

  // 工具函数
  utils: {
    // 云存储管理器
    cloudStorage: cloudStorage,

    // 格式化时间
    formatTime(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      const minute = date.getMinutes();
      const second = date.getSeconds();

      return `${[year, month, day].map(this.formatNumber).join('/')} ${[hour, minute, second].map(this.formatNumber).join(':')}`;
    },

    formatNumber(n) {
      n = n.toString();
      return n[1] ? n : `0${n}`;
    },

    // 显示加载提示
    showLoading(title = '加载中...') {
      wx.showLoading({
        title,
        mask: true
      });
    },

    // 隐藏加载提示
    hideLoading() {
      wx.hideLoading();
    },

    // 显示成功提示
    showSuccess(title) {
      wx.showToast({
        title,
        icon: 'success',
        duration: 2000
      });
    },

    // 统一的错误处理方法
    handleError(error, context = '应用错误') {
      try {
        if (this.globalData && this.globalData.errorHandler) {
          this.globalData.errorHandler.handle(error, context);
        } else {
          console.error(`[${context}] 错误:`, error);
          wx.showToast({
            title: typeof error === 'string' ? error : (error.message || '操作失败'),
            icon: 'none',
            duration: 2000
          });
        }
      } catch (handlerError) {
        console.error('错误处理器本身出错:', handlerError);
        wx.showToast({
          title: typeof error === 'string' ? error : (error.message || '操作失败'),
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 显示错误提示（兼容旧接口）
    showError(title, context = '应用错误') {
      this.handleError(new Error(title), context);
    },

  },

  // 启动全局聊天消息监听器
  startGlobalChatWatcher() {
    // 延迟启动，确保云开发已初始化且用户已登录
    setTimeout(() => {
      this.setupGlobalChatWatcher();
    }, 3000);
  },

  // 设置全局聊天消息监听器
  setupGlobalChatWatcher() {
    if (!this.globalData.cloudReady || !this.globalData.userInfo || !this.globalData.userInfo._id) {
      console.log('📱 [全局聊天监听] 条件不满足，延迟启动');
      setTimeout(() => {
        this.setupGlobalChatWatcher();
      }, 2000);
      return;
    }

    console.log('📱 [全局聊天监听] 启动全局聊天消息监听器，用户ID:', this.globalData.userInfo._id);

    try {
      // 监听当前用户参与的所有聊天室的新消息
      this.globalChatWatcher = wx.cloud.database().collection('messages')
        .where({
          chatRoomId: wx.cloud.database().command.exists(true),
          // 不监听自己发送的消息
          senderId: wx.cloud.database().command.neq(this.globalData.userInfo._id)
        })
        .watch({
          onChange: (snapshot) => {
            console.log('📱 [全局聊天监听] 收到消息变更:', {
              type: snapshot.type,
              docChanges: snapshot.docChanges?.length || 0
            });

            if (snapshot.type === 'init') {
              console.log('📱 [全局聊天监听] 跳过初始化数据');
              return;
            }

            // 处理新消息
            if (snapshot.docChanges) {
              snapshot.docChanges.forEach(change => {
                if (change.queueType === 'enqueue' || change.queueType === 'update') {
                  this.handleNewChatMessage(change.doc);
                }
              });
            }
          },
          onError: (error) => {
            console.error('❌ [全局聊天监听] 监听失败:', error);
            // 重新启动监听器
            setTimeout(() => {
              this.setupGlobalChatWatcher();
            }, 5000);
          }
        });

      console.log('✅ [全局聊天监听] 监听器启动成功');
    } catch (error) {
      console.error('❌ [全局聊天监听] 启动失败:', error);
    }
  },

  // 处理新聊天消息
  async handleNewChatMessage(messageData) {
    const { chatRoomId, senderId, content, type, _id } = messageData;

    // 消息去重：检查是否已经处理过这条消息
    if (!this.processedChatMessages) {
      this.processedChatMessages = new Set();
    }

    if (this.processedChatMessages.has(_id)) {
      return;
    }

    // 记录已处理的消息ID
    this.processedChatMessages.add(_id);

    // 限制缓存大小，避免内存泄漏
    if (this.processedChatMessages.size > 1000) {
      const firstItem = this.processedChatMessages.values().next().value;
      this.processedChatMessages.delete(firstItem);
    }



    // 检查是否是发给当前用户的消息
    const isMessageForCurrentUser = await this.checkIfMessageForCurrentUser(chatRoomId);

    if (!isMessageForCurrentUser) {

      return;
    }

    // 检查当前用户是否在该聊天室页面
    const isInChatRoom = this.globalData.activeChatRoomId === chatRoomId;

    if (isInChatRoom) {
      console.log('📱 [全局聊天监听] 用户在聊天室内，跳过未读数更新');
      return;
    }

    // 更新未读消息数（增加1条未读消息）
    this.globalData.unreadMessages.updateUnread(chatRoomId, 1);

    // 触发通知提醒
    await this.triggerChatMessageNotification(messageData, chatRoomId);

    console.log('📱 [全局聊天监听] 未读消息已更新');
  },

  // 检查消息是否是发给当前用户的
  async checkIfMessageForCurrentUser(chatRoomId) {
    try {
      // 使用云函数检查聊天室参与者（避免前端权限问题）
      const result = await wx.cloud.callFunction({
        name: 'chatRoom',
        data: {
          action: 'checkParticipant',
          chatRoomId: chatRoomId,
          userId: this.globalData.userInfo._id
        }
      });

      if (result.result && result.result.success) {
        return result.result.isParticipant;
      }

      return false;
    } catch (error) {
      console.error('📱 [全局聊天监听] 检查聊天室参与者失败:', error);
      return false;
    }
  },

  // 触发聊天消息通知
  async triggerChatMessageNotification(messageData, chatRoomId) {
    try {
      const { senderId, content, type } = messageData;

      // 获取发送者信息
      const senderInfo = await this.getSenderInfo(senderId);
      const senderName = senderInfo?.nickName || '未知用户';

      // 检查通知偏好设置
      const notificationPrefs = this.getNotificationPreferences();

      if (!notificationPrefs.chatMessage) {
        return;
      }

      // 准备通知内容
      const notificationContent = this.formatMessageContent(content, type);
      const notificationTitle = `${senderName}`;

      // 显示本地通知
      this.showChatNotification({
        title: notificationTitle,
        content: notificationContent,
        chatRoomId: chatRoomId,
        messageData: messageData
      });

      // 触发声音和震动
      if (notificationPrefs.sound) {
        this.playNotificationSound();
      }

      if (notificationPrefs.vibrate) {
        this.triggerNotificationVibration();
      }



    } catch (error) {
      console.error('📱 [通知] 触发聊天消息通知失败:', error);
    }
  },

  // 获取发送者信息
  async getSenderInfo(senderId) {
    try {
      const result = await wx.cloud.database().collection('users')
        .doc(senderId)
        .field({ nickName: true, avatarUrl: true })
        .get();

      return result.data;
    } catch (error) {
      console.error('📱 [通知] 获取发送者信息失败:', error);
      return null;
    }
  },

  // 格式化消息内容
  formatMessageContent(content, type) {
    switch (type) {
      case 'text':
        return content.length > 20 ? content.substring(0, 20) + '...' : content;
      case 'image':
        return '[图片]';
      case 'voice':
        return '[语音]';
      case 'video':
        return '[视频]';
      case 'file':
        return '[文件]';
      default:
        return '[消息]';
    }
  },

  // 显示聊天通知
  showChatNotification(options) {
    const { title, content, chatRoomId, messageData } = options;

    // 检查是否在前台
    if (this.globalData.appInBackground) {
      // 如果在后台，显示系统通知（需要订阅消息权限）
      this.showSystemNotification({ title, content });
    } else {
      // 如果在前台，显示应用内通知
      this.showInAppNotification({ title, content, chatRoomId, messageData });
    }
  },

  // 显示应用内通知
  showInAppNotification(options) {
    const { title, content, chatRoomId, messageData } = options;

    // 触发全局事件，让页面显示自定义通知组件
    this.$emit('showInAppNotification', {
      type: 'message',
      title,
      content,
      chatRoomId,
      messageData,
      timestamp: new Date()
    });

    // 备用方案：如果没有页面监听事件，使用系统Toast
    setTimeout(() => {
      if (!this.notificationHandled) {
        wx.showToast({
          title: `${title}: ${content}`,
          icon: 'none',
          duration: 1500, // 1.5秒，快速显示
          mask: false
        });
      }
      this.notificationHandled = false;
    }, 100);
  },

  // 显示系统通知
  showSystemNotification(options) {
    // 这里可以集成微信的订阅消息功能
  },

  // 播放通知声音
  playNotificationSound() {
    try {
      // 使用微信内置的提示音
      wx.playVoice({
        filePath: 'notification_sound', // 这里需要预设的音频文件
        success: () => {},
        fail: (error) => {
          // 备用方案：使用系统提示音
          wx.showToast({
            title: '',
            icon: 'none',
            duration: 1
          });
        }
      });
    } catch (error) {
      console.error('📱 [通知] 播放通知声音失败:', error);
    }
  },

  // 触发通知震动
  triggerNotificationVibration() {
    try {
      wx.vibrateShort({
        type: 'medium', // light, medium, heavy
        success: () => {},
        fail: (error) => {}
      });
    } catch (error) {
      console.error('📱 [通知] 触发震动失败:', error);
    }
  },

  // 获取通知偏好设置
  getNotificationPreferences() {
    const defaultPrefs = {
      chatMessage: true,
      orderStatus: true,
      newOrder: true,
      evaluation: true,
      system: true,
      sound: true,
      vibrate: true
    };

    try {
      const stored = wx.getStorageSync('notificationPreferences');
      return stored ? { ...defaultPrefs, ...stored } : defaultPrefs;
    } catch (error) {
      console.error('📱 [通知] 获取通知偏好失败:', error);
      return defaultPrefs;
    }
  },

  // 设置通知偏好
  setNotificationPreferences(preferences) {
    try {
      const current = this.getNotificationPreferences();
      const updated = { ...current, ...preferences };
      wx.setStorageSync('notificationPreferences', updated);
    } catch (error) {
      console.error('📱 [通知] 设置通知偏好失败:', error);
    }
  },

  // 获取隐私设置
  getPrivacySettings() {
    const defaultSettings = {
      allowStrangerContact: false,
      showOnlineStatus: true
    };

    try {
      const stored = wx.getStorageSync('privacySettings');
      return stored ? { ...defaultSettings, ...stored } : defaultSettings;
    } catch (error) {
      console.error('🔐 [隐私] 获取隐私设置失败:', error);
      return defaultSettings;
    }
  },

  // 设置隐私偏好
  setPrivacySettings(settings) {
    try {
      const current = this.getPrivacySettings();
      const updated = { ...current, ...settings };
      wx.setStorageSync('privacySettings', updated);

      // 触发隐私设置变更事件
      this.$emit('privacySettingsChanged', updated);
    } catch (error) {
      console.error('🔐 [隐私] 设置隐私偏好失败:', error);
    }
  },

  // 检查离线期间的新消息
  async checkOfflineMessages() {
    console.log('📱 [离线消息检查] 方法被调用');

    if (!this.globalData.userInfo || !this.globalData.userInfo._id) {
      console.log('📱 [离线消息检查] 用户信息不存在，跳过检查');
      console.log('📱 [离线消息检查] userInfo:', this.globalData.userInfo);
      return;
    }

    // 确定检查时间：使用隐藏时间作为离线消息检查的起始时间
    // 这样可以确保检查用户离线期间的所有新消息
    let checkTime = this.globalData.lastHideTime;

    if (!checkTime) {
      console.log('📱 [离线消息检查] 无时间记录，跳过首次检查');
      // 首次启动时，设置当前时间为最后检查时间，避免检查历史消息
      this.globalData.lastHideTime = new Date();
      this.globalData.lastActiveTime = new Date();
      console.log('📱 [离线消息检查] 初始化时间记录');
      return; // 首次启动不检查离线消息
    }

    console.log('📱 [离线消息检查] 开始检查离线期间的新消息');
    console.log('📱 [离线消息检查] 用户ID:', this.globalData.userInfo._id);
    console.log('📱 [离线消息检查] 检查时间:', checkTime);
    console.log('📱 [离线消息检查] 最后活跃时间:', this.globalData.lastActiveTime);
    console.log('📱 [离线消息检查] 最后隐藏时间:', this.globalData.lastHideTime);
    console.log('📱 [离线消息检查] 当前未读状态:', {
      total: this.globalData.unreadMessages.total,
      chatRooms: Object.keys(this.globalData.unreadMessages.chatRooms).length,
      details: this.globalData.unreadMessages.chatRooms
    });

    try {
      // 调用云函数检查离线期间的新消息
      const result = await wx.cloud.callFunction({
        name: 'chatMessage',
        data: {
          action: 'checkOfflineMessages',
          lastCheckTime: checkTime
        }
      });

      console.log('📱 [离线消息检查] 云函数调用结果:', result);
      console.log('📱 [离线消息检查] result.result:', result.result);

      if (result.result && result.result.success) {
        const offlineMessages = result.result.data || [];
        console.log('📱 [离线消息检查] 发现离线消息:', offlineMessages.length, '条');

        if (offlineMessages.length > 0) {
          console.log('📱 [离线消息检查] 离线消息详情:', offlineMessages.map(msg => ({
            _id: msg._id,
            chatRoomId: msg.chatRoomId,
            senderId: msg.senderId,
            content: msg.content
          })));
        }

        // 处理离线消息，更新未读状态
        let processedCount = 0;

        // 确保消息去重集合存在
        if (!this.processedChatMessages) {
          this.processedChatMessages = new Set();
        }

        offlineMessages.forEach(message => {
          const { chatRoomId, senderId, _id } = message;

          console.log('📱 [离线消息检查] 处理消息:', {
            messageId: _id,
            chatRoomId,
            senderId,
            currentUserId: this.globalData.userInfo._id,
            isOwnMessage: senderId === this.globalData.userInfo._id,
            alreadyProcessed: this.processedChatMessages.has(_id)
          });

          // 消息去重：检查是否已经处理过这条消息
          if (this.processedChatMessages.has(_id)) {
            console.log('📱 [离线消息检查] 消息已处理过，跳过:', _id);
            return;
          }

          // 确保不是自己发送的消息
          if (senderId !== this.globalData.userInfo._id) {
            // 记录已处理的消息ID
            this.processedChatMessages.add(_id);

            // 更新未读消息数
            this.globalData.unreadMessages.updateUnread(chatRoomId, 1);

            // 更新最后消息信息
            this.globalData.unreadMessages.updateLastMessage(chatRoomId, message);

            processedCount++;
            console.log('📱 [离线消息检查] 已处理消息，聊天室:', chatRoomId);
          }
        });

        console.log('📱 [离线消息检查] 总共处理了', processedCount, '条消息');

        // 强制保存未读消息状态到本地存储
        try {
          wx.setStorageSync('unreadMessages', this.globalData.unreadMessages);
          console.log('📱 [离线消息检查] 强制保存未读状态到本地存储');
        } catch (error) {
          console.error('📱 [离线消息检查] 保存未读状态失败:', error);
        }

        // 更新最后活跃时间，但保持lastHideTime不变，直到下次真正隐藏
        const now = new Date();
        this.globalData.lastActiveTime = now;
        console.log('📱 [离线消息检查] 更新最后活跃时间:', now);
        console.log('📱 [离线消息检查] 保持隐藏时间不变:', this.globalData.lastHideTime);
        console.log('📱 [离线消息检查] 最终未读状态:', {
          total: this.globalData.unreadMessages.total,
          chatRooms: Object.keys(this.globalData.unreadMessages.chatRooms).length
        });

        // 如果有新消息，触发全局事件通知相关页面更新
        if (processedCount > 0) {
          // 触发未读消息更新事件
          this.$emit('unreadMessageUpdate', {
            chatRoomId: 'global',
            unreadData: this.globalData.unreadMessages
          });

          // 触发离线消息检测事件
          this.$emit('offlineMessagesDetected', {
            messageCount: processedCount,
            messages: offlineMessages
          });

          console.log('📱 [离线消息检查] 已更新未读状态，触发页面刷新事件');

          // 延迟强制更新TabBar，确保未读数正确显示
          setTimeout(() => {
            this.$emit('unreadMessageUpdate', {
              chatRoomId: 'forceUpdate',
              unreadData: this.globalData.unreadMessages
            });
            console.log('📱 [离线消息检查] 强制更新TabBar未读数:', this.globalData.unreadMessages.total);
          }, 500);
        } else {
          console.log('📱 [离线消息检查] 没有需要处理的新消息');
        }
      } else {
        console.error('📱 [离线消息检查] 云函数调用失败:', result);
        if (result.result) {
          console.error('📱 [离线消息检查] 错误详情:', result.result.error);
        }
      }
    } catch (error) {
      console.error('📱 [离线消息检查] 检查失败:', error);
    }
  },

  // 更新最后检查时间（当用户阅读消息后调用）
  updateLastCheckTime() {
    const now = new Date();
    this.globalData.lastHideTime = now;
    this.globalData.lastActiveTime = now;
    console.log('📱 [时间更新] 更新最后活跃时间:', now);
  }

})

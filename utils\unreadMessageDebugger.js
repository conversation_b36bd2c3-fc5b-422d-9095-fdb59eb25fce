/**
 * 未读消息调试工具
 * 用于快速检查和诊断未读消息状态
 */

class UnreadMessageDebugger {
  constructor() {
    this.app = getApp();
  }

  /**
   * 获取当前未读消息状态的完整信息
   */
  getFullStatus() {
    const unreadData = this.app.globalData.unreadMessages;
    const status = {
      timestamp: new Date().toISOString(),
      total: unreadData.total,
      chatRoomsCount: Object.keys(unreadData.chatRooms).length,
      chatRooms: {},
      lastUpdateTime: unreadData.lastUpdateTime,
      appState: {
        userInfo: !!this.app.globalData.userInfo,
        userId: this.app.globalData.userInfo?._id,
        lastHideTime: this.app.globalData.lastHideTime,
        lastActiveTime: this.app.globalData.lastActiveTime,
        activeChatRoomId: this.app.globalData.activeChatRoomId
      }
    };

    // 详细的聊天室未读信息
    Object.keys(unreadData.chatRooms).forEach(chatRoomId => {
      const roomData = unreadData.chatRooms[chatRoomId];
      status.chatRooms[chatRoomId] = {
        count: roomData.count,
        lastMessage: roomData.lastMessage,
        lastUpdateTime: roomData.lastUpdateTime
      };
    });

    return status;
  }

  /**
   * 打印当前状态到控制台
   */
  printStatus() {
    const status = this.getFullStatus();
    console.log('📊 [未读消息调试] 当前状态:', JSON.stringify(status, null, 2));
    return status;
  }

  /**
   * 检查本地存储中的未读消息数据
   */
  checkLocalStorage() {
    try {
      const cachedData = wx.getStorageSync('unreadMessages');
      console.log('💾 [未读消息调试] 本地存储数据:', cachedData);
      return cachedData;
    } catch (error) {
      console.error('❌ [未读消息调试] 读取本地存储失败:', error);
      return null;
    }
  }

  /**
   * 强制刷新未读消息状态
   */
  forceRefresh() {
    console.log('🔄 [未读消息调试] 强制刷新未读消息状态');
    
    // 触发未读消息更新事件
    this.app.$emit('unreadMessageUpdate', {
      chatRoomId: 'debug_refresh',
      unreadData: this.app.globalData.unreadMessages
    });

    // 强制保存到本地存储
    try {
      wx.setStorageSync('unreadMessages', this.app.globalData.unreadMessages);
      console.log('✅ [未读消息调试] 强制保存到本地存储成功');
    } catch (error) {
      console.error('❌ [未读消息调试] 强制保存失败:', error);
    }
  }

  /**
   * 模拟离线消息检查
   */
  async simulateOfflineCheck() {
    console.log('🧪 [未读消息调试] 模拟离线消息检查');
    
    if (this.app.checkOfflineMessages) {
      await this.app.checkOfflineMessages();
    } else {
      console.error('❌ [未读消息调试] checkOfflineMessages 方法不存在');
    }
  }

  /**
   * 清除所有未读消息状态（用于测试）
   */
  clearAllUnread() {
    console.log('🧹 [未读消息调试] 清除所有未读消息状态');
    
    const unreadData = this.app.globalData.unreadMessages;
    unreadData.total = 0;
    unreadData.chatRooms = {};
    unreadData.lastUpdateTime = new Date();

    // 保存到本地存储
    try {
      wx.setStorageSync('unreadMessages', unreadData);
    } catch (error) {
      console.error('❌ [未读消息调试] 保存清除状态失败:', error);
    }

    // 触发更新事件
    this.app.$emit('unreadMessageUpdate', {
      chatRoomId: 'debug_clear',
      unreadData: unreadData
    });

    console.log('✅ [未读消息调试] 未读消息状态已清除');
  }

  /**
   * 添加测试未读消息（用于测试）
   */
  addTestUnread(chatRoomId, count = 1) {
    console.log(`🧪 [未读消息调试] 添加测试未读消息: ${chatRoomId}, 数量: ${count}`);
    
    const unreadData = this.app.globalData.unreadMessages;
    unreadData.updateUnread(chatRoomId, count);
    
    console.log('✅ [未读消息调试] 测试未读消息已添加');
  }

  /**
   * 获取调试报告
   */
  getDebugReport() {
    const report = {
      currentStatus: this.getFullStatus(),
      localStorage: this.checkLocalStorage(),
      timestamp: new Date().toISOString()
    };

    console.log('📋 [未读消息调试] 调试报告:', JSON.stringify(report, null, 2));
    return report;
  }
}

// 创建全局调试实例
const debugger = new UnreadMessageDebugger();

// 在控制台中提供快捷方法
if (typeof console !== 'undefined') {
  console.unreadDebug = {
    status: () => debugger.printStatus(),
    refresh: () => debugger.forceRefresh(),
    check: () => debugger.simulateOfflineCheck(),
    clear: () => debugger.clearAllUnread(),
    addTest: (chatRoomId, count) => debugger.addTestUnread(chatRoomId, count),
    report: () => debugger.getDebugReport(),
    storage: () => debugger.checkLocalStorage()
  };

  console.log('🛠️ [未读消息调试] 调试工具已加载，使用 console.unreadDebug 访问调试方法');
  console.log('📖 [未读消息调试] 可用方法: status(), refresh(), check(), clear(), addTest(), report(), storage()');
}

module.exports = UnreadMessageDebugger;
